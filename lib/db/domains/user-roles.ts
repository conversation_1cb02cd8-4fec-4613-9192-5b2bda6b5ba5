import { supabaseAdmin } from '../../supabase'
import { debug } from '../../debug'
import type { UserRole } from '../../schemas'

// User Roles - Now using unified appy_managers table
export async function getUserRole(userId: string): Promise<UserRole | null> {
  debug.log('🔍 Getting user role for:', userId)

  const { data: managerData, error } = await supabaseAdmin.from('appy_managers')
    .select('user_id, role, created_at')
    .eq('user_id', userId)
    .single()

  if (error) {
    debug.log('❌ User role not found for:', userId)
    return null
  }

  debug.log('✅ Found user role:', managerData)
  return managerData.role as UserRole
}

export async function createUserRole(userId: string, role: 'super-admin' | 'hr-admin' | 'manager' | 'accountant'): Promise<UserRole> {
  debug.log('🔄 Creating user role:', { userId, role })

  // Check if manager exists, if not create them first
  const { data: existingManager } = await supabaseAdmin.from('appy_managers')
    .select('user_id')
    .eq('user_id', userId)
    .single()

  if (!existingManager) {
    throw new Error('Manager must exist before assigning role. Create manager first.')
  }

  // Update the role in appy_managers
  const { data, error } = await supabaseAdmin.from('appy_managers')
    .update({ role })
    .eq('user_id', userId)
    .select('role')
    .single()

  if (error) {
    console.error('❌ Failed to create user role:', error)
    throw new Error(error.message)
  }

  debug.log('✅ User role created successfully')
  return data.role as UserRole
}

export async function updateUserRole(userId: string, role: 'super-admin' | 'hr-admin' | 'manager' | 'accountant'): Promise<UserRole> {
  debug.log('🔄 Updating user role:', { userId, role })

  const { data, error } = await supabaseAdmin.from('appy_managers')
    .update({ role })
    .eq('user_id', userId)
    .select('role')
    .single()

  if (error) {
    console.error('❌ Failed to update user role:', error)
    throw new Error(error.message)
  }

  debug.log('✅ User role updated successfully')
  return data.role as UserRole
}