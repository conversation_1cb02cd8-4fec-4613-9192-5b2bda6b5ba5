import { supabaseAdmin } from './supabase'

export interface AdminProfile {
  id: string
  fullName: string
  email: string
  role: 'super-admin' | 'admin'
  supervisorId?: string
  supervisorName?: string
  permissions: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface AdminPermissions {
  canManageUsers: boolean
  canViewReports: boolean
  canExportData: boolean
  canManageSettings: boolean
  canViewAllAdmins: boolean
  canEditAdmins: boolean
}

// Removed hardcoded admin profiles - all data now comes from database

// Function to get admin profile by user ID with database fallback
async function getAdminProfileFromDatabase(userId: string): Promise<AdminProfile | null> {
  try {
    console.log('🔍 [ADMIN-DATA] Looking for admin profile with userId:', userId)

    const { data: user, error: userError } = await supabaseAdmin
      .from('appy_managers')
      .select('*')
      .eq('user_id', userId)
      .single()

    console.log('👤 [ADMIN-DATA] Manager query result:', { user, error: userError })

    if (!user) return null

    const role = user.role as 'super-admin' | 'admin' || 'admin'

    const profile = {
      id: userId,
      fullName: user.full_name,
      email: user.email,
      role,
      supervisorId: undefined,
      supervisorName: undefined,
      permissions: getPermissionsForRole(role),
      createdAt: user.created_at,
      updatedAt: user.created_at
    }

    console.log('✅ [ADMIN-DATA] Created admin profile:', profile)
    return profile
  } catch (error) {
    console.error('Error fetching admin from database:', error)
    return null
  }
}

// Helper function to get permissions based on role
function getPermissionsForRole(role: 'super-admin' | 'admin'): AdminPermissions {
  if (role === 'super-admin') {
    return {
      canManageUsers: true,
      canViewReports: true,
      canExportData: true,
      canManageSettings: true,
      canViewAllAdmins: true,
      canEditAdmins: true
    }
  }
  
  return {
    canManageUsers: true,
    canViewReports: true,
    canExportData: true,
    canManageSettings: false,
    canViewAllAdmins: false,
    canEditAdmins: false
  }
}

export async function getAdminProfile(adminId: string): Promise<AdminProfile | null> {
  // First try to get from database
  const dbProfile = await getAdminProfileFromDatabase(adminId)
  if (dbProfile) {
    return dbProfile
  }
  
  // Fallback to mock data (for development)
  // This won't work in production since we don't have the actual user IDs
  console.warn('Using mock admin data - this should be replaced with database queries')
  return null
}

export async function getAdminPermissions(adminId: string): Promise<AdminPermissions> {
  const admin = await getAdminProfile(adminId)
  
  if (!admin) {
    return {
      canManageUsers: false,
      canViewReports: false,
      canExportData: false,
      canManageSettings: false,
      canViewAllAdmins: false,
      canEditAdmins: false
    }
  }
  
  return admin.permissions as AdminPermissions
}

export async function getAllAdmins(currentUserId?: string, currentUserRole?: string): Promise<AdminProfile[]> {
  if (!currentUserId || !currentUserRole) return []
  
  try {
    // Super-admin can see all admins
    if (currentUserRole === 'super-admin') {
      console.log('🔍 [ADMIN-DATA] Fetching all admins for super-admin')

      // Get all managers with their roles
      const { data: managers, error: managersError } = await supabaseAdmin
        .from('appy_managers')
        .select('user_id, full_name, email, created_at, role')
        .eq('active', true)

      console.log('👥 [ADMIN-DATA] Managers query result:', { managers, error: managersError })

      if (managers && managers.length > 0) {
        const adminProfiles = managers.map(manager => ({
          id: manager.user_id,
          fullName: manager.full_name,
          email: manager.email,
          role: (manager.role as 'super-admin' | 'admin') || 'admin',
          supervisorId: undefined,
          supervisorName: undefined,
          permissions: getPermissionsForRole(manager.role || 'admin'),
          createdAt: manager.created_at,
          updatedAt: manager.created_at
        }))

        console.log('✅ [ADMIN-DATA] Created admin profiles:', adminProfiles)
        return adminProfiles
      }
    }
    
    // Regular admins can only see themselves
    const profile = await getAdminProfile(currentUserId)
    return profile ? [profile] : []
  } catch (error) {
    console.error('Error fetching admins:', error)
    return []
  }
}

export async function canAccessAdminPage(adminId: string, currentUserId?: string, currentUserRole?: string): Promise<boolean> {
  if (!currentUserId || !currentUserRole) return false
  
  // Super-admin can access all admin pages
  if (currentUserRole === 'super-admin') {
    return true
  }
  
  // Regular admins can only access their own page
  return currentUserId === adminId
}

export async function getAdminStats(adminId: string, currentUserId?: string, currentUserRole?: string) {
  if (!currentUserId || !currentUserRole || !await canAccessAdminPage(adminId, currentUserId, currentUserRole)) {
    return null
  }

  try {
    console.log('📊 [ADMIN-DATA] Fetching admin stats')

    // Get total employees
    const { data: employees, error: employeesError } = await supabaseAdmin
      .from('appy_employees')
      .select('id')
      .eq('active', true)

    // Get total departments
    const { data: departments, error: deptsError } = await supabaseAdmin
      .from('appy_departments')
      .select('id')

    // Get total managers
    const { data: managers, error: managersError } = await supabaseAdmin
      .from('appy_managers')
      .select('user_id')
      .eq('active', true)

    // Get appraisal stats
    const { data: appraisals, error: appraisalsError } = await supabaseAdmin
      .from('appy_appraisals')
      .select('status')

    console.log('📊 [ADMIN-DATA] Raw stats:', {
      employees: employees?.length || 0,
      departments: departments?.length || 0,
      managers: managers?.length || 0,
      appraisals: appraisals?.length || 0
    })

    const totalEmployees = employees?.length || 0
    const departmentCount = departments?.length || 0
    const managerCount = managers?.length || 0

    const completedAppraisals = appraisals?.filter(a => a.status === 'submitted').length || 0
    const pendingAppraisals = appraisals?.filter(a => a.status === 'pending').length || 0
    const activeAppraisals = completedAppraisals + pendingAppraisals

    const approvalRate = activeAppraisals > 0 ? (completedAppraisals / activeAppraisals) * 100 : 0

    const stats = {
      totalEmployees,
      activeAppraisals,
      completedAppraisals,
      pendingAppraisals,
      departmentCount,
      managerCount,
      monthlyGrowth: 0, // Would need historical data to calculate
      approvalRate: Math.round(approvalRate * 10) / 10
    }

    console.log('✅ [ADMIN-DATA] Calculated stats:', stats)
    return stats

  } catch (error) {
    console.error('❌ [ADMIN-DATA] Error fetching admin stats:', error)
    // Return fallback stats
    return {
      totalEmployees: 0,
      activeAppraisals: 0,
      completedAppraisals: 0,
      pendingAppraisals: 0,
      departmentCount: 0,
      managerCount: 0,
      monthlyGrowth: 0,
      approvalRate: 0
    }
  }
}

export async function updateAdminProfile(adminId: string, updates: Partial<AdminProfile>, currentUserRole?: string): Promise<AdminProfile | null> {
  if (!currentUserRole || currentUserRole !== 'super-admin') {
    throw new Error('Insufficient permissions')
  }
  
  try {
    // Update manager profile
    if (updates.fullName || updates.email) {
      const { error } = await supabaseAdmin
        .from('appy_managers')
        .update({
          full_name: updates.fullName,
          email: updates.email
        })
        .eq('user_id', adminId)
      
      if (error) throw error
    }
    
    // Update role if provided
    if (updates.role) {
      const { error } = await supabaseAdmin
        .from('appy_managers')
        .update({ role: updates.role })
        .eq('user_id', adminId)

      if (error) throw error
    }
    
    // Return updated profile
    return await getAdminProfile(adminId)
  } catch (error) {
    console.error('Error updating admin profile:', error)
    throw new Error('Failed to update admin profile')
  }
}

export async function getAdminByEmail(email: string): Promise<AdminProfile | null> {
  // First try to get from database
  try {
    const { data: user } = await supabaseAdmin
      .from('appy_managers')
      .select('*')
      .eq('email', email)
      .single()

    if (user) {
      const role = user.role as 'super-admin' | 'admin' || 'admin'

      return {
        id: user.user_id,
        fullName: user.full_name,
        email: user.email,
        role,
        supervisorId: undefined,
        supervisorName: undefined,
        permissions: getPermissionsForRole(role),
        createdAt: user.created_at,
        updatedAt: user.created_at
      }
    }
  } catch (error) {
    console.error('Error fetching admin by email:', error)
  }

  return null
}