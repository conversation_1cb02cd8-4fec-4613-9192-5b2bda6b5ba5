# Appraisal System - Complete Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture](#architecture)
3. [Database Schema](#database-schema)
4. [Core Features](#core-features)
5. [Multi-Level Approval System](#multi-level-approval-system)
6. [HR Management](#hr-management)
7. [Accounting Integration](#accounting-integration)
8. [Authentication & Authorization](#authentication--authorization)
9. [API Structure](#api-structure)
10. [User Roles & Permissions](#user-roles--permissions)

## System Overview

The Appraisal System is a comprehensive employee performance management platform built with Next.js 15, React 19, and Supabase. It provides end-to-end functionality for conducting employee appraisals, managing HR processes, handling multi-level approvals, and integrating with accounting systems.

### Key Features
- **Employee Performance Appraisals** with customizable templates
- **Multi-Level Approval Workflows** with delegation support
- **HR Management** including employee feedback and PTO tracking
- **Accounting Integration** for compensation and payment processing
- **Role-Based Access Control** with hierarchical permissions
- **Email Notifications** and scheduled reminders
- **Audit Logging** for compliance and tracking

## Architecture

### Frontend Stack
- **Framework**: Next.js 15 with App Router
- **UI Library**: React 19 with shadcn/ui components
- **Styling**: Tailwind CSS
- **State Management**: React Hook Form for forms, server actions for mutations
- **Authentication**: Clerk integration with custom role management

### Backend Stack
- **Database**: Supabase (PostgreSQL)
- **API**: Next.js Server Actions and API Routes
- **Authentication**: Clerk with Supabase integration
- **Email**: Integrated email notification system
- **File Storage**: Supabase Storage for attachments

### Project Structure
```
├── app/                    # Next.js App Router pages
├── components/            # Reusable UI components
├── lib/
│   ├── auth.ts           # Authentication utilities
│   ├── supabase.ts       # Database client and types
│   ├── db/               # Database operations (modular)
│   │   ├── domains/      # Domain-specific database functions
│   │   └── core/         # Core database utilities
│   ├── data/             # Data access layer
│   └── schemas.ts        # Zod validation schemas
├── docs/                 # Documentation
└── middleware.ts         # Authentication middleware
```

## Database Schema

The system uses a comprehensive PostgreSQL schema with 25+ tables organized around core domains:

### Core Tables

#### Employee Management
- **`appy_employees`**: Core employee information
- **`appy_departments`**: Organizational departments
- **`appy_managers`**: Manager information with Clerk integration
- **`appy_employee_managers`**: Many-to-many employee-manager relationships
- **`appy_employee_kpis`**: Employee key performance indicators

#### Appraisal System
- **`appy_appraisal_periods`**: Time periods for appraisals
- **`appy_appraisals`**: Core appraisal data with comprehensive fields
- **`appy_appraisal_templates`**: Customizable appraisal templates
- **`appy_template_usage`**: Template usage tracking

#### Multi-Level Approval System
- **`appy_approval_workflows`**: Workflow orchestration
- **`appy_approval_steps`**: Individual approval steps
- **`appy_approval_history`**: Complete audit trail
- **`appy_approval_delegations`**: Approval delegation management

#### HR Management
- **`appy_employee_feedback`**: Employee feedback and complaints
- **`appy_feedback_comments`**: Threaded feedback discussions
- **`appy_feedback_attachments`**: File attachments for feedback
- **`appy_feedback_status_history`**: Feedback status tracking

#### PTO Management
- **`appy_employee_pto_balances`**: Employee PTO balances
- **`appy_pto_requests`**: PTO request management

#### Communication System
- **`appy_email_templates`**: Email template management
- **`appy_email_settings`**: Email configuration
- **`appy_notification_log`**: Email delivery tracking
- **`appy_scheduled_notifications`**: Scheduled email system

#### System Administration
- **`appy_audit_log`**: System-wide audit logging

### Key Relationships

```mermaid
erDiagram
    appy_employees ||--o{ appy_appraisals : "has"
    appy_managers ||--o{ appy_appraisals : "conducts"
    appy_appraisal_periods ||--o{ appy_appraisals : "contains"
    appy_departments ||--o{ appy_employees : "belongs_to"
    appy_appraisals ||--o| appy_approval_workflows : "triggers"
    appy_approval_workflows ||--o{ appy_approval_steps : "contains"
    appy_employees ||--o{ appy_employee_feedback : "submits"
    appy_employees ||--o{ appy_pto_requests : "requests"
```

### Custom Enums

- **`appy_appraisal_status`**: draft, submitted, completed, pending, ready-to-pay, contact-manager
- **`appy_user_role`**: super-admin, hr-admin, manager, accountant
- **`appy_compensation_type`**: hourly, monthly, yearly
- **`appy_pto_request_type`**: vacation, sick, personal, emergency
- **`appy_pto_status`**: pending, approved, rejected, cancelled

## Core Features

### 1. Employee Appraisal System

#### Comprehensive Appraisal Form
The appraisal system includes detailed evaluation criteria:

**Core Questions (Legacy)**
- Question 1-5: Traditional appraisal questions

**Enhanced Evaluation Fields**
- **Key Contributions**: Major achievements and contributions
- **Extra Initiatives**: Additional projects and initiatives
- **Performance Areas**: Areas needing improvement
- **Discipline Rating**: 1-5 scale with comments
- **Days Off Tracking**: Actual days taken
- **Performance Ratings** (1-5 scale each):
  - Impact Rating & Comments
  - Quality Rating & Comments  
  - Collaboration Rating & Comments
  - Skill Growth Rating & Comments
- **Promotion Readiness**: Multiple choice assessment
- **Compensation Recommendation**: Salary/rate recommendations

#### Revision System
- **Unlimited Resubmissions**: Managers can revise appraisals indefinitely
- **Revision Tracking**: `revision_number`, `is_revision`, `original_submission_date`
- **Edit History**: `last_edited_at` timestamp tracking

#### Template System
- **Customizable Templates**: JSONB-based flexible question structures
- **Department-Specific**: Templates can be filtered by department
- **Role-Based**: Templates can target specific roles
- **Version Control**: Template versioning system
- **Usage Analytics**: Track template usage across periods

### 2. Employee Management

#### Employee Profiles
- **Personal Information**: Full name, email, bio, social links
- **Employment Details**: Department, manager, compensation, rate type
- **KPI Tracking**: Flexible key performance indicators
- **Status Management**: Active/inactive employee status

#### Hierarchical Management
- **Manager Relationships**: Many-to-many employee-manager assignments
- **Primary Manager**: Designated primary manager per employee
- **Department Structure**: Organizational department hierarchy
- **Role-Based Access**: Managers see only their direct reports (unless super-admin)

### 3. PTO (Paid Time Off) System

#### PTO Balance Management
- **Annual Balances**: Year-based PTO allocation (default 7 days)
- **Usage Tracking**: Automatic deduction from balances
- **Balance Calculations**: Available days = Total - Used

#### PTO Request Workflow
- **Request Types**: Vacation, sick, personal, emergency
- **Approval Process**: Manager approval required
- **Status Tracking**: Pending, approved, rejected, cancelled
- **Date Range Support**: Multi-day PTO requests

## Multi-Level Approval System

### Workflow Architecture

The system supports sophisticated multi-level approval workflows:

#### Workflow Components
1. **`appy_approval_workflows`**: Master workflow record
2. **`appy_approval_steps`**: Individual approval steps in sequence
3. **`appy_approval_history`**: Complete audit trail of all actions
4. **`appy_approval_delegations`**: Temporary approval delegation

#### Workflow Types
- **Standard**: Basic single-level approval
- **Multi-Level**: Sequential approval through multiple levels
- **Parallel**: Multiple approvers at same level (future enhancement)

#### Approval Process Flow

```mermaid
sequenceDiagram
    participant E as Employee/Manager
    participant W as Workflow Engine
    participant A1 as Approver Level 1
    participant A2 as Approver Level 2
    participant S as System

    E->>W: Submit Appraisal
    W->>W: Create Workflow
    W->>A1: Notify Level 1 Approver
    A1->>W: Approve/Reject
    alt Approved
        W->>A2: Notify Level 2 Approver
        A2->>W: Final Approve/Reject
        W->>S: Update Appraisal Status
    else Rejected
        W->>E: Notify Rejection
        W->>S: Update Status to Rejected
    end
```

#### Delegation System
- **Temporary Delegation**: Approvers can delegate authority
- **Reason Tracking**: Delegation reasons recorded
- **Expiration**: Time-limited delegations
- **Workflow-Specific**: Delegations can be workflow-specific or general

### Approval Features

#### Step Management
- **Sequential Processing**: Steps processed in order by level
- **Status Tracking**: pending, approved, rejected, delegated
- **Comments**: Approvers can add comments at each step
- **Timestamps**: Detailed timing of all approval actions

#### History & Audit
- **Complete Audit Trail**: Every action logged with actor, timestamps
- **Status Changes**: Previous and new status tracking
- **Metadata**: Additional context stored as JSONB
- **Actor Information**: Full actor details (ID, name, role)

## HR Management

### Employee Feedback System

#### Feedback Categories
- **Feedback Types**: Complaint, suggestion, recognition, concern
- **Categories**: Performance, workplace, management, policy
- **Priority Levels**: Low, medium, high, urgent

#### Feedback Workflow
1. **Submission**: Employees submit feedback (anonymous option available)
2. **Review**: HR reviews and categorizes feedback
3. **Investigation**: HR investigates and gathers information
4. **Resolution**: HR provides resolution and closes feedback
5. **Follow-up**: Optional follow-up scheduling

#### Features
- **Anonymous Submissions**: Protect employee identity
- **File Attachments**: Support for evidence/documentation
- **Threaded Comments**: Internal HR discussions
- **Status Tracking**: pending, under-review, resolved, closed
- **Follow-up System**: Scheduled follow-up reminders

### HR Analytics & Reporting

#### Feedback Analytics
- **Volume Tracking**: Feedback submission trends
- **Category Analysis**: Most common feedback types
- **Resolution Times**: Average time to resolution
- **Satisfaction Metrics**: Post-resolution feedback

#### Employee Insights
- **Performance Trends**: Appraisal score analysis
- **PTO Usage**: Time-off pattern analysis
- **Feedback Patterns**: Employee satisfaction indicators

## Accounting Integration

### Compensation Management

#### Employee Compensation
- **Rate Types**: Hourly, monthly, yearly compensation
- **Compensation Tracking**: Current rates stored with employees
- **Rate History**: Historical compensation data (via audit log)

#### Payment Processing Integration

#### Appraisal-Based Payments
- **Payment Status**: ready-to-pay, contact-manager flags
- **Compensation Recommendations**: Manager salary/rate suggestions
- **Approval Integration**: Payment approval through workflow system

#### Accounting Features
- **Export Capabilities**: Appraisal data export for accounting systems
- **Payment Tracking**: Integration with payment status
- **Compensation Analysis**: Rate comparison and analysis tools
- **Budget Planning**: Compensation forecasting based on appraisals

### Financial Reporting

#### Compensation Reports
- **Department Costs**: Total compensation by department
- **Rate Analysis**: Compensation distribution analysis
- **Budget Variance**: Actual vs. planned compensation
- **Appraisal Impact**: Performance-based compensation changes

#### Integration Points
- **API Endpoints**: RESTful APIs for accounting system integration
- **Data Export**: CSV/JSON export capabilities
- **Webhook Support**: Real-time payment status updates
- **Audit Trail**: Complete financial audit logging

## Authentication & Authorization

### Clerk Integration

#### Authentication Flow
1. **Clerk Authentication**: Primary authentication via Clerk
2. **Database Sync**: User data synchronized with `appy_managers`
3. **Role Assignment**: Roles managed in database, not Clerk metadata
4. **Session Management**: Clerk handles session, app handles authorization

#### User Synchronization
- **Email-Based Matching**: Clerk users matched to database via email
- **Automatic Creation**: New managers created automatically on first login
- **Profile Sync**: Name and email synchronized from Clerk

### Role-Based Access Control

#### User Roles
- **Super Admin**: Full system access, can view all data
- **HR Admin**: HR functions, employee management, feedback system
- **Manager**: Appraisal management for direct reports
- **Accountant**: Read-only access to appraisals and compensation data

#### Permission System

```typescript
const rolePermissions = {
  'super-admin': ['*'], // All permissions
  'hr-admin': [
    'employee:*', 'feedback:*', 'pto:*', 
    'appraisal:read', 'department:*'
  ],
  'manager': [
    'employee:read', 'appraisal:*', 
    'pto:read', 'pto:approve'
  ],
  'accountant': [
    'employee:read', 'appraisal:read', 
    'approval:read', 'approval:export'
  ]
}
```

#### Data Scoping
- **Hierarchical Access**: Managers see only their direct reports
- **Department Filtering**: HR can filter by department
- **Global Search**: Proper scoping based on user role and hierarchy

## API Structure

### Database Layer Architecture

#### Modular Organization
```
lib/db/
├── core/
│   ├── types.ts      # TypeScript type definitions
│   ├── utils.ts      # Database utilities
│   └── health.ts     # Health check functions
└── domains/
    ├── employees.ts   # Employee operations
    ├── appraisals.ts  # Appraisal operations
    ├── managers.ts    # Manager operations
    ├── feedback.ts    # HR feedback operations
    ├── pto.ts         # PTO operations
    ├── templates.ts   # Template operations
    └── workflows.ts   # Approval workflows
```

#### Database Functions

**Employee Management**
- `getEmployees()`, `createEmployee()`, `updateEmployee()`
- `getEmployeeById()`, `getEmployeeByClerkId()`
- `softDeleteEmployee()`, `hardDeleteEmployee()`

**Appraisal Operations**
- `getAppraisalByEmployeeId()`, `createOrUpdateAppraisal()`
- `submitAppraisal()`, `approveAppraisal()`, `rejectAppraisal()`
- `getAppraisalsWithEmployeeData()`

**Workflow Management**
- `createApprovalWorkflow()`, `processApprovalStep()`
- `delegateApproval()`, `getWorkflowHistory()`

### Server Actions

#### Form Handling
- **Type-Safe**: Zod schema validation for all inputs
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Cache Revalidation**: Automatic cache invalidation after mutations

#### Key Server Actions
- `saveEmployee()`, `saveAppraisal()`, `submitAppraisal()`
- `approveAppraisal()`, `rejectAppraisal()`
- `createFeedback()`, `updateFeedbackStatus()`
- `createPTORequest()`, `approvePTORequest()`

## User Roles & Permissions

### Detailed Permission Matrix

| Feature | Super Admin | HR Admin | Manager | Accountant |
|---------|-------------|----------|---------|------------|
| View All Employees | ✅ | ✅ | ❌ (Direct reports only) | ✅ (Read-only) |
| Create/Edit Employees | ✅ | ✅ | ❌ | ❌ |
| Conduct Appraisals | ✅ | ❌ | ✅ (Direct reports) | ❌ |
| View All Appraisals | ✅ | ✅ (Read-only) | ❌ (Own appraisals) | ✅ (Read-only) |
| Approve Appraisals | ✅ | ❌ | ✅ (Via workflow) | ❌ |
| Manage Feedback | ✅ | ✅ | ❌ | ❌ |
| PTO Management | ✅ | ✅ | ✅ (Approve for reports) | ❌ |
| System Configuration | ✅ | ❌ | ❌ | ❌ |
| Export Data | ✅ | ✅ | ❌ | ✅ |

### Access Control Implementation

#### Route Protection
```typescript
// Middleware-based protection
export async function requireRole(requiredRole: UserRole | UserRole[]) {
  const user = await getCurrentUser()
  if (!allowedRoles.includes(user.role)) {
    redirect('/dashboard?error=insufficient_permissions')
  }
}

// Permission-based protection
export async function requirePermission(permission: string) {
  const user = await getCurrentUser()
  if (!hasPermission(user.role, permission)) {
    redirect('/dashboard?error=insufficient_permissions')
  }
}
```

#### Data Filtering
```typescript
// Hierarchical data access
export async function getEmployeesForUser(userId: string, role: UserRole) {
  if (role === 'super-admin') {
    return getAllEmployees()
  } else if (role === 'manager') {
    return getDirectReports(userId)
  }
  // Additional role-based filtering...
}
```

## Email Notification System

### Notification Architecture

#### Email Templates
- **Template Management**: Customizable email templates stored in `appy_email_templates`
- **Variable Substitution**: Dynamic content using template variables
- **Template Types**: notification, reminder, approval, feedback
- **Multi-Language Support**: Template localization capabilities

#### Scheduled Notifications
- **Automated Reminders**: 4 days before month-end for incomplete appraisals
- **Workflow Notifications**: Approval request notifications
- **Status Updates**: Appraisal status change notifications
- **PTO Notifications**: PTO request and approval notifications

#### Email Settings & Configuration
- **SMTP Configuration**: Email server settings in `appy_email_settings`
- **Delivery Tracking**: Email delivery status in `appy_notification_log`
- **Error Handling**: Failed email tracking and retry logic
- **Rate Limiting**: Email sending rate controls

### Notification Types

#### Appraisal Notifications
- **Pending Appraisals**: Monthly reminders to managers
- **Submission Confirmations**: Appraisal submission acknowledgments
- **Approval Requests**: Multi-level approval notifications
- **Status Updates**: Completion and payment status updates

#### HR Notifications
- **Feedback Submissions**: New feedback notifications to HR
- **PTO Requests**: PTO approval request notifications
- **System Alerts**: Critical system notifications

## System Monitoring & Logging

### Audit System

#### Comprehensive Audit Trail
- **Action Logging**: All CRUD operations logged in `appy_audit_log`
- **User Tracking**: Complete user action history
- **Data Changes**: Before/after values for all modifications
- **Timestamp Tracking**: Precise timing of all system events

#### Audit Log Structure
```sql
CREATE TABLE appy_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  action VARCHAR(50) NOT NULL,           -- INSERT, UPDATE, DELETE
  table_name VARCHAR(100) NOT NULL,      -- Affected table
  record_id VARCHAR(100),                -- Affected record ID
  old_values JSONB,                      -- Previous values
  new_values JSONB,                      -- New values
  user_id VARCHAR(100) NOT NULL,         -- Acting user
  created_at TIMESTAMPTZ DEFAULT now()
);
```

### Performance Monitoring

#### Database Performance
- **Query Optimization**: Indexed foreign keys and search columns
- **Connection Pooling**: Supabase connection management
- **Query Caching**: Strategic caching for frequently accessed data

#### Application Monitoring
- **Error Tracking**: Comprehensive error logging
- **Performance Metrics**: Response time monitoring
- **User Activity**: Session and usage analytics

## Security & Compliance

### Data Security

#### Authentication Security
- **Clerk Integration**: Enterprise-grade authentication
- **Session Management**: Secure session handling
- **Password Policies**: Enforced through Clerk
- **Multi-Factor Authentication**: Available through Clerk

#### Authorization Security
- **Role-Based Access**: Strict role-based permissions
- **Data Scoping**: Users see only authorized data
- **API Security**: Protected server actions and API routes
- **Input Validation**: Zod schema validation for all inputs

#### Database Security
- **Row Level Security**: Supabase RLS policies
- **Encrypted Storage**: Data encryption at rest
- **Secure Connections**: SSL/TLS for all database connections
- **Backup Security**: Encrypted database backups

### Compliance Features

#### Audit Compliance
- **Complete Audit Trail**: All actions logged and traceable
- **Data Retention**: Configurable data retention policies
- **Export Capabilities**: Audit data export for compliance reporting
- **User Activity Reports**: Detailed user activity reporting

#### Privacy Compliance
- **Data Anonymization**: Anonymous feedback options
- **Data Export**: Employee data export capabilities
- **Data Deletion**: Soft and hard delete options
- **Consent Management**: Employee consent tracking

## Deployment & Infrastructure

### Environment Configuration

#### Environment Variables
```bash
# Database
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Authentication
CLERK_SECRET_KEY=your_clerk_secret
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key

# Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

#### Database Setup
1. **Supabase Project**: Create new Supabase project
2. **Schema Migration**: Run SQL schema creation scripts
3. **RLS Policies**: Configure Row Level Security
4. **Indexes**: Create performance indexes
5. **Functions**: Deploy database functions

### Deployment Architecture

#### Production Stack
- **Frontend**: Vercel/Netlify deployment
- **Database**: Supabase managed PostgreSQL
- **Authentication**: Clerk managed authentication
- **Storage**: Supabase Storage for file attachments
- **Email**: Integrated email service (SendGrid/Resend)

#### Scaling Considerations
- **Database Scaling**: Supabase auto-scaling
- **CDN**: Static asset delivery optimization
- **Caching**: Redis caching for frequently accessed data
- **Load Balancing**: Application load balancing

## Development Workflow

### Code Organization

#### TypeScript Configuration
- **Strict Mode**: Full TypeScript strict mode enabled
- **Path Mapping**: `@/*` aliases for clean imports
- **Type Safety**: Comprehensive type definitions
- **Schema Validation**: Zod schemas for runtime validation

#### Component Architecture
- **Reusable Components**: shadcn/ui component library
- **Server Components**: Next.js 15 server components
- **Client Components**: Minimal client-side JavaScript
- **Form Handling**: React Hook Form with Zod validation

### Testing Strategy

#### Unit Testing
- **Component Testing**: React component unit tests
- **Function Testing**: Database function testing
- **Schema Testing**: Validation schema testing
- **Utility Testing**: Helper function testing

#### Integration Testing
- **API Testing**: Server action integration tests
- **Database Testing**: Database operation testing
- **Authentication Testing**: Auth flow testing
- **Workflow Testing**: Approval workflow testing

### Maintenance & Updates

#### Regular Maintenance
- **Database Cleanup**: Periodic audit log cleanup
- **Performance Review**: Regular performance analysis
- **Security Updates**: Dependency security updates
- **Backup Verification**: Regular backup testing

#### Feature Development
- **Feature Flags**: Gradual feature rollout
- **A/B Testing**: Feature effectiveness testing
- **User Feedback**: Continuous user feedback collection
- **Iterative Improvement**: Regular feature enhancements

## Troubleshooting Guide

### Common Issues

#### Authentication Issues
- **Clerk Sync Problems**: User not found in database
- **Permission Errors**: Insufficient role permissions
- **Session Timeout**: Authentication session expired

#### Database Issues
- **Connection Errors**: Supabase connection problems
- **Query Timeouts**: Long-running query optimization
- **Foreign Key Violations**: Data integrity issues

#### Performance Issues
- **Slow Queries**: Database query optimization
- **Memory Usage**: Application memory optimization
- **Load Times**: Frontend performance optimization

### Debug Tools

#### Logging System
- **Console Logging**: Conditional debug logging
- **Error Tracking**: Comprehensive error logging
- **Performance Logging**: Query and response time logging
- **User Action Logging**: User interaction tracking

#### Development Tools
- **Database Inspector**: Supabase database inspector
- **Query Analyzer**: SQL query performance analysis
- **Network Monitor**: API request/response monitoring
- **Component Inspector**: React component debugging

This comprehensive documentation covers all aspects of the appraisal system, from architecture and features to deployment and maintenance. The system is designed to be enterprise-ready with robust security, scalability, and maintainability features.
