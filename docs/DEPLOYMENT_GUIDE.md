# Deployment Guide

## Prerequisites

- Node.js 18+ 
- Bun package manager
- Supabase account
- Clerk account
- Git repository access

## Environment Setup

### 1. Clone Repository
```bash
git clone https://github.com/OxFrancesco/appraisal-tool.git
cd appraisal-tool
```

### 2. Install Dependencies
```bash
bun install
```

### 3. Environment Variables

Create `.env.local` file in the project root:

```bash
# Database Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Authentication
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key

# Application
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Optional: Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_SMTP_HOST=smtp.yourprovider.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USER=your_smtp_user
EMAIL_SMTP_PASS=your_smtp_password
```

## Database Setup

### 1. Create Supabase Project

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Create new project
3. Note down the project URL and API keys
4. Go to Project Settings → API to get keys

### 2. Database Schema Setup

Execute the following SQL in Supabase SQL Editor:

```sql
-- Create custom enums
CREATE TYPE appy_user_role AS ENUM ('super-admin', 'hr-admin', 'manager', 'accountant');
CREATE TYPE appy_appraisal_status AS ENUM ('draft', 'submitted', 'completed', 'pending', 'ready-to-pay', 'contact-manager');
CREATE TYPE appy_compensation_type AS ENUM ('hourly', 'monthly', 'yearly');
CREATE TYPE appy_pto_request_type AS ENUM ('vacation', 'sick', 'personal', 'emergency');
CREATE TYPE appy_pto_status AS ENUM ('pending', 'approved', 'rejected', 'cancelled');

-- Create departments table
CREATE TABLE appy_departments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Create managers table
CREATE TABLE appy_managers (
  user_id TEXT PRIMARY KEY, -- Clerk user ID
  full_name TEXT NOT NULL,
  email TEXT NOT NULL,
  department_id UUID REFERENCES appy_departments(id),
  manager_id TEXT REFERENCES appy_managers(user_id),
  role appy_user_role DEFAULT 'manager',
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Create employees table
CREATE TABLE appy_employees (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  full_name TEXT NOT NULL,
  first_name VARCHAR(255),
  last_name VARCHAR(255),
  email VARCHAR(255) NOT NULL UNIQUE,
  role VARCHAR(100),
  bio TEXT,
  linkedin_url VARCHAR(500),
  twitter_url VARCHAR(500),
  telegram_url VARCHAR(255),
  compensation NUMERIC NOT NULL,
  rate appy_compensation_type NOT NULL,
  department_id UUID REFERENCES appy_departments(id),
  manager_id TEXT REFERENCES appy_managers(user_id),
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Continue with remaining tables...
-- (See DATABASE_SCHEMA.md for complete schema)
```

### 3. Row Level Security (RLS)

Enable RLS and create policies:

```sql
-- Enable RLS on all tables
ALTER TABLE appy_employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_appraisals ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_managers ENABLE ROW LEVEL SECURITY;
-- ... enable for all tables

-- Create RLS policies
CREATE POLICY "Users can view their own data" ON appy_employees
  FOR SELECT USING (auth.uid()::text = manager_id);

CREATE POLICY "Managers can view their reports" ON appy_employees
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM appy_employee_managers em
      WHERE em.employee_id = appy_employees.id
      AND em.manager_id = auth.uid()::text
    )
  );

-- Add more policies as needed
```

### 4. Database Functions

Create helper functions:

```sql
-- Function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_id TEXT)
RETURNS appy_user_role AS $$
BEGIN
  RETURN (
    SELECT role FROM appy_managers 
    WHERE appy_managers.user_id = get_user_role.user_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is super admin
CREATE OR REPLACE FUNCTION is_super_admin(user_id TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN get_user_role(user_id) = 'super-admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Authentication Setup

### 1. Clerk Configuration

1. Create Clerk application at [Clerk Dashboard](https://dashboard.clerk.com)
2. Configure sign-in/sign-up options
3. Set up redirect URLs:
   - Development: `http://localhost:3000`
   - Production: `https://yourdomain.com`

### 2. Clerk Webhook Setup (Optional)

For user synchronization:

1. Go to Clerk Dashboard → Webhooks
2. Add endpoint: `https://yourdomain.com/api/webhooks/clerk`
3. Select events: `user.created`, `user.updated`
4. Copy webhook secret to environment variables

### 3. User Role Management

Initial super admin setup:

```sql
-- Insert initial super admin
INSERT INTO appy_managers (user_id, full_name, email, role)
VALUES ('your_clerk_user_id', 'Your Name', '<EMAIL>', 'super-admin');
```

## Development

### 1. Start Development Server
```bash
bun dev
```

### 2. Database Migrations

For schema changes:

```bash
# Generate migration
bun run db:generate

# Apply migration
bun run db:migrate
```

### 3. Type Generation

Update TypeScript types after schema changes:

```bash
bun run types:generate
```

## Production Deployment

### 1. Vercel Deployment

#### Automatic Deployment
1. Connect GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

#### Manual Deployment
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### 2. Environment Variables in Production

Set in Vercel dashboard or deployment platform:

```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key
CLERK_SECRET_KEY=sk_live_your_production_clerk_secret
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_live_your_production_clerk_key
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://yourdomain.com
```

### 3. Database Configuration

#### Production Database
1. Upgrade Supabase project to Pro (if needed)
2. Configure connection pooling
3. Set up database backups
4. Configure monitoring and alerts

#### Performance Optimization
```sql
-- Create indexes for better performance
CREATE INDEX idx_employees_manager_id ON appy_employees(manager_id);
CREATE INDEX idx_appraisals_employee_id ON appy_appraisals(employee_id);
CREATE INDEX idx_appraisals_period_id ON appy_appraisals(period_id);
CREATE INDEX idx_appraisals_status ON appy_appraisals(status);
CREATE INDEX idx_feedback_status ON appy_employee_feedback(status);
CREATE INDEX idx_audit_log_created_at ON appy_audit_log(created_at);
```

## Post-Deployment Setup

### 1. Initial Data Setup

```sql
-- Create default departments
INSERT INTO appy_departments (name) VALUES 
  ('Engineering'),
  ('Human Resources'),
  ('Marketing'),
  ('Sales'),
  ('Operations');

-- Create default appraisal period
INSERT INTO appy_appraisal_periods (name, start_date, end_date, status)
VALUES ('Q1 2024', '2024-01-01', '2024-03-31', 'active');

-- Create default email templates
INSERT INTO appy_email_templates (template_key, template_name, subject_template, body_template)
VALUES (
  'appraisal_reminder',
  'Appraisal Reminder',
  'Reminder: Complete appraisals for {{employeeCount}} employees',
  'Hi {{managerName}}, you have {{employeeCount}} pending appraisals due by {{deadline}}.'
);
```

### 2. User Onboarding

1. Create initial super admin user
2. Set up department structure
3. Import employee data
4. Assign managers to employees
5. Create appraisal periods

### 3. System Configuration

```sql
-- Configure email settings
INSERT INTO appy_email_settings (setting_key, setting_value, description)
VALUES 
  ('smtp_host', 'smtp.yourprovider.com', 'SMTP server host'),
  ('smtp_port', '587', 'SMTP server port'),
  ('from_email', '<EMAIL>', 'Default from email'),
  ('reminder_days', '4', 'Days before deadline to send reminders');
```

## Monitoring & Maintenance

### 1. Health Checks

Create monitoring endpoints:

```typescript
// app/api/health/route.ts
export async function GET() {
  try {
    // Check database connection
    const { data, error } = await supabase
      .from('appy_departments')
      .select('count')
      .limit(1)
    
    if (error) throw error
    
    return Response.json({ status: 'healthy', timestamp: new Date().toISOString() })
  } catch (error) {
    return Response.json(
      { status: 'unhealthy', error: error.message },
      { status: 500 }
    )
  }
}
```

### 2. Backup Strategy

1. **Automated Backups**: Supabase provides automatic backups
2. **Manual Backups**: Regular database exports
3. **Data Export**: Regular export of critical data

### 3. Performance Monitoring

1. **Database Performance**: Monitor query performance in Supabase
2. **Application Performance**: Use Vercel Analytics
3. **Error Tracking**: Implement error tracking (Sentry, etc.)

### 4. Security Monitoring

1. **Access Logs**: Monitor user access patterns
2. **Failed Logins**: Track authentication failures
3. **Data Changes**: Monitor audit logs for suspicious activity

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check environment variables
echo $NEXT_PUBLIC_SUPABASE_URL
echo $SUPABASE_SERVICE_ROLE_KEY

# Test database connection
bun run db:test
```

#### Authentication Issues
```bash
# Verify Clerk configuration
echo $CLERK_SECRET_KEY
echo $NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY

# Check Clerk webhook endpoint
curl -X POST https://yourdomain.com/api/webhooks/clerk
```

#### Build Issues
```bash
# Clear Next.js cache
rm -rf .next

# Reinstall dependencies
rm -rf node_modules
bun install

# Rebuild
bun run build
```

### Logs and Debugging

#### Application Logs
```bash
# View Vercel logs
vercel logs

# View local development logs
bun dev --debug
```

#### Database Logs
1. Go to Supabase Dashboard
2. Navigate to Logs section
3. Filter by error level and time range

This deployment guide covers the complete setup process from development to production. Follow the steps in order and ensure all environment variables and configurations are properly set before deploying to production.
