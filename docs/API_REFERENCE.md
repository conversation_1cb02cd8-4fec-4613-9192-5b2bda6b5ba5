# API Reference Guide

## Overview

The appraisal system uses Next.js Server Actions for data mutations and direct database queries for data fetching. All operations are type-safe with Zod validation and comprehensive error handling.

## Authentication

### Current User
```typescript
import { getCurrentUser } from '@/lib/auth'

const user = await getCurrentUser()
// Returns: { id, email, fullName, role, imageUrl }
```

### Role Checking
```typescript
import { hasPermission, requireRole } from '@/lib/auth'

// Check permissions
const canEdit = hasPermission(user.role, 'employee:write')

// Require specific role (throws/redirects if insufficient)
await requireRole(['super-admin', 'hr-admin'])
```

## Employee Management

### Get Employees
```typescript
import { getEmployees } from '@/lib/data/employees'

// Get all employees (respects user role/hierarchy)
const employees = await getEmployees()

// Get employees for specific manager
const managerEmployees = await getEmployeesForManager(managerId)
```

### Create/Update Employee
```typescript
import { saveEmployee } from '@/lib/data/employees'

const employeeData = {
  fullName: "<PERSON>",
  email: "<EMAIL>",
  departmentId: "dept-uuid",
  managerId: "manager-clerk-id",
  compensation: 5000,
  rate: "monthly" as const,
  role: "Developer"
}

await saveEmployee(employeeData)
```

### Employee Details
```typescript
import { getEmployeeDetails } from '@/lib/data/employees'

const employee = await getEmployeeDetails(employeeId)
// Returns employee with department, manager, and KPI data
```

## Appraisal System

### Get Appraisals
```typescript
import { getManagerAppraisals } from '@/lib/data/appraisals'

// Get appraisals for current user (respects role hierarchy)
const appraisals = await getManagerAppraisals()
```

### Create/Update Appraisal
```typescript
import { db } from '@/lib/db'

const appraisalData = {
  periodId: "period-uuid",
  employeeId: "employee-uuid", 
  managerId: "manager-clerk-id",
  keyContributions: "Major project delivery...",
  extraInitiatives: "Led training sessions...",
  performanceLacking: "Time management...",
  disciplineRating: 4,
  disciplineComment: "Excellent discipline...",
  daysOffTaken: 5,
  impactRating: 5,
  impactComment: "High impact contributions...",
  qualityRating: 4,
  qualityComment: "Consistent quality...",
  collaborationRating: 5,
  collaborationComment: "Great team player...",
  skillGrowthRating: 4,
  skillGrowthComment: "Continuous learning...",
  readinessPromotion: "ready",
  readinessComment: "Ready for senior role...",
  compensationRecommendation: "10% increase recommended",
  paymentStatus: "ready-to-pay"
}

const appraisal = await db.createOrUpdateAppraisal(appraisalData)
```

### Submit Appraisal
```typescript
import { db } from '@/lib/db'

await db.submitAppraisal(appraisalId, paymentStatus)
```

### Approve/Reject Appraisal
```typescript
import { approveAppraisal, rejectAppraisal } from '@/lib/data/appraisals'

// Approve
await approveAppraisal(appraisalId, comments)

// Reject
await rejectAppraisal(appraisalId, reason)
```

## Department Management

### Get Departments
```typescript
import { getDepartments } from '@/lib/data/departments'

const departments = await getDepartments()
```

### Create/Update Department
```typescript
import { saveDepartment } from '@/lib/data/departments'

await saveDepartment({ name: "Engineering" })
```

## Manager Operations

### Get Managers
```typescript
import { getManagers } from '@/lib/data/managers'

const managers = await getManagers()
```

### Ensure Manager Exists
```typescript
import { db } from '@/lib/db'

// Create manager if doesn't exist (used in auth flow)
await db.ensureManagerExists(clerkUserId, email, fullName)
```

## HR Management

### Employee Feedback

#### Create Feedback
```typescript
import { db } from '@/lib/db'

const feedbackData = {
  submitterId: "employee-uuid",
  targetEmployeeId: "target-employee-uuid", // optional
  feedbackType: "complaint",
  category: "workplace",
  subject: "Workplace concern",
  message: "Detailed feedback message...",
  isAnonymous: false,
  priority: "medium"
}

await db.createFeedback(feedbackData)
```

#### Get Feedback
```typescript
import { db } from '@/lib/db'

// Get all feedback (HR view)
const feedback = await db.getFeedback()

// Get feedback by ID
const feedbackItem = await db.getFeedbackById(feedbackId)

// Get pending feedback for HR
const pendingFeedback = await db.getPendingFeedbackForHR()
```

#### Update Feedback Status
```typescript
import { db } from '@/lib/db'

await db.updateFeedbackStatus(feedbackId, 'under-review', reviewerId)
```

#### Add Feedback Comments
```typescript
import { db } from '@/lib/db'

await db.addFeedbackComment(feedbackId, commenterId, comment, isInternal)
```

### PTO Management

#### Get PTO Balance
```typescript
import { db } from '@/lib/db'

const balance = await db.getPTOBalance(employeeId, year)
```

#### Create PTO Request
```typescript
import { db } from '@/lib/db'

const ptoData = {
  employeeId: "employee-uuid",
  managerId: "manager-clerk-id",
  requestType: "vacation",
  startDate: "2024-07-01",
  endDate: "2024-07-05",
  daysRequested: 5,
  reason: "Family vacation"
}

await db.createPTORequest(ptoData)
```

#### Approve/Reject PTO
```typescript
import { db } from '@/lib/db'

// Approve
await db.approvePTORequest(requestId, approverId)

// Reject
await db.rejectPTORequest(requestId, approverId, rejectionReason)
```

## Multi-Level Approval Workflows

### Create Approval Workflow
```typescript
import { db } from '@/lib/db'

const workflowData = {
  appraisalId: "appraisal-uuid",
  workflowType: "multi-level",
  steps: [
    { level: 1, approverId: "manager1-id", approverRole: "manager" },
    { level: 2, approverId: "hr-admin-id", approverRole: "hr-admin" }
  ]
}

await db.createApprovalWorkflow(workflowData)
```

### Process Approval Step
```typescript
import { db } from '@/lib/db'

// Approve step
await db.processApprovalStep(stepId, 'approved', approverId, comments)

// Reject step
await db.processApprovalStep(stepId, 'rejected', approverId, rejectionReason)
```

### Delegate Approval
```typescript
import { db } from '@/lib/db'

const delegationData = {
  originalApproverId: "original-approver-id",
  delegateApproverId: "delegate-approver-id",
  workflowId: "workflow-uuid", // optional - for specific workflow
  delegationReason: "Out of office",
  expiresAt: "2024-08-01T00:00:00Z"
}

await db.delegateApproval(delegationData)
```

## Template Management

### Get Templates
```typescript
import { db } from '@/lib/db'

// Get all templates
const templates = await db.getTemplates()

// Get templates with filters
const deptTemplates = await db.getTemplates({ 
  departmentId: "dept-uuid",
  isActive: true 
})

// Get default template
const defaultTemplate = await db.getDefaultTemplate()
```

### Create Template
```typescript
import { db } from '@/lib/db'

const templateData = {
  name: "Engineering Appraisal Template",
  description: "Template for engineering roles",
  questions: [
    {
      id: "technical_skills",
      type: "rating",
      question: "Rate technical skills (1-5)",
      required: true
    },
    {
      id: "code_quality", 
      type: "text",
      question: "Describe code quality improvements",
      required: false
    }
  ],
  departmentId: "engineering-dept-uuid",
  roleFilter: "developer,senior-developer",
  isActive: true,
  isDefault: false,
  createdBy: "manager-clerk-id"
}

await db.createTemplate(templateData)
```

## Email Notifications

### Schedule Notification
```typescript
import { db } from '@/lib/db'

const notificationData = {
  notificationType: "appraisal_reminder",
  recipientId: "manager-clerk-id",
  recipientEmail: "<EMAIL>",
  templateKey: "appraisal_reminder",
  templateData: {
    managerName: "John Manager",
    employeeCount: 3,
    deadline: "2024-07-31"
  },
  scheduledFor: "2024-07-27T09:00:00Z"
}

await db.scheduleNotification(notificationData)
```

### Get Email Templates
```typescript
import { db } from '@/lib/db'

const templates = await db.getEmailTemplates()
const template = await db.getEmailTemplateByKey("appraisal_reminder")
```

## Audit & Logging

### Query Audit Log
```typescript
import { db } from '@/lib/db'

// Get audit log with filters
const auditLog = await db.getAuditLog({
  tableName: "appy_appraisals",
  userId: "user-id",
  startDate: "2024-07-01",
  endDate: "2024-07-31"
})
```

## Error Handling

### Standard Error Response
```typescript
try {
  await someOperation()
} catch (error) {
  if (error instanceof Error) {
    // Handle specific error types
    if (error.message.includes('unique constraint')) {
      throw new Error('Record already exists')
    }
    if (error.message.includes('foreign key')) {
      throw new Error('Invalid reference')
    }
  }
  throw new Error('Operation failed')
}
```

### Validation Errors
```typescript
import { z } from 'zod'
import { employeeSchema } from '@/lib/schemas'

try {
  const validatedData = employeeSchema.parse(formData)
} catch (error) {
  if (error instanceof z.ZodError) {
    // Handle validation errors
    const fieldErrors = error.flatten().fieldErrors
    return { errors: fieldErrors }
  }
}
```

## Common Query Patterns

### Hierarchical Data Access
```typescript
// Get employees based on user role and hierarchy
const getEmployeesForUser = async (userId: string, role: UserRole) => {
  if (role === 'super-admin') {
    return db.getEmployees() // All employees
  } else if (role === 'manager') {
    return db.getEmployeesForManager(userId) // Direct reports only
  } else {
    return [] // No access
  }
}
```

### Filtered Queries
```typescript
// Get appraisals with complex filtering
const getFilteredAppraisals = async (filters: {
  periodId?: string
  status?: string
  departmentId?: string
  managerId?: string
}) => {
  return db.getAppraisalsWithFilters(filters)
}
```

### Aggregated Data
```typescript
// Get appraisal statistics
const getAppraisalStats = async (periodId: string) => {
  return db.getAppraisalStatistics(periodId)
  // Returns: { total, completed, pending, averageRatings }
}
```

This API reference covers the most common operations in the appraisal system. All functions include proper error handling, type safety, and respect user permissions and data hierarchy.
