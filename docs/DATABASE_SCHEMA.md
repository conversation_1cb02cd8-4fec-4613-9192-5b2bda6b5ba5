# Database Schema Reference

## Overview

The appraisal system uses a comprehensive PostgreSQL database schema with 25+ tables organized around core business domains. All tables use the `appy_` prefix for namespace isolation.

## Table Relationships Diagram

```mermaid
erDiagram
    appy_departments ||--o{ appy_employees : belongs_to
    appy_departments ||--o{ appy_managers : belongs_to
    appy_employees ||--o{ appy_appraisals : evaluated_in
    appy_managers ||--o{ appy_appraisals : conducts
    appy_appraisal_periods ||--o{ appy_appraisals : contains
    appy_appraisal_templates ||--o{ appy_appraisals : uses
    appy_employees ||--o{ appy_employee_managers : assigned_to
    appy_managers ||--o{ appy_employee_managers : manages
    appy_employees ||--o{ appy_employee_kpis : has
    appy_employees ||--o{ appy_employee_pto_balances : has
    appy_employees ||--o{ appy_pto_requests : submits
    appy_employees ||--o{ appy_employee_feedback : submits
    appy_appraisals ||--o| appy_approval_workflows : triggers
    appy_approval_workflows ||--o{ appy_approval_steps : contains
    appy_approval_workflows ||--o{ appy_approval_history : logs
    appy_employee_feedback ||--o{ appy_feedback_comments : has
    appy_employee_feedback ||--o{ appy_feedback_attachments : has
```

## Core Tables

### Employee Management

#### appy_employees
Core employee information and employment details.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique employee identifier |
| full_name | TEXT | NOT NULL | Employee full name |
| first_name | VARCHAR(255) | NULL | Employee first name |
| last_name | VARCHAR(255) | NULL | Employee last name |
| email | VARCHAR(255) | NOT NULL, UNIQUE | Employee email address |
| role | VARCHAR(100) | NULL | Job role/title |
| bio | TEXT | NULL | Employee biography |
| linkedin_url | VARCHAR(500) | NULL | LinkedIn profile URL |
| twitter_url | VARCHAR(500) | NULL | Twitter profile URL |
| telegram_url | VARCHAR(255) | NULL | Telegram profile URL |
| compensation | NUMERIC | NOT NULL | Employee compensation amount |
| rate | appy_compensation_type | NOT NULL | Compensation rate type (hourly/monthly/yearly) |
| department_id | UUID | FK to appy_departments.id | Department assignment |
| manager_id | TEXT | FK to appy_managers.user_id | Primary manager |
| active | BOOLEAN | DEFAULT true | Employee active status |
| created_at | TIMESTAMPTZ | DEFAULT now() | Record creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT now() | Last update timestamp |

#### appy_departments
Organizational department structure.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Department identifier |
| name | TEXT | NOT NULL | Department name |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |

#### appy_managers
Manager information with Clerk integration.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| user_id | TEXT | PRIMARY KEY | Clerk user ID |
| full_name | TEXT | NOT NULL | Manager full name |
| email | TEXT | NOT NULL | Manager email address |
| department_id | UUID | FK to appy_departments.id | Manager's department |
| manager_id | TEXT | FK to appy_managers.user_id | Manager's manager (hierarchy) |
| role | appy_user_role | DEFAULT 'manager' | System role |
| active | BOOLEAN | DEFAULT true | Manager active status |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |

#### appy_employee_managers
Many-to-many relationship between employees and managers.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Relationship identifier |
| employee_id | UUID | FK to appy_employees.id | Employee reference |
| manager_id | TEXT | FK to appy_managers.user_id | Manager reference |
| is_primary | BOOLEAN | DEFAULT false | Primary manager flag |
| assigned_at | TIMESTAMPTZ | DEFAULT now() | Assignment timestamp |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |

### Appraisal System

#### appy_appraisals
Core appraisal data with comprehensive evaluation fields.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Appraisal identifier |
| employee_id | UUID | NOT NULL, FK to appy_employees.id | Employee being appraised |
| period_id | UUID | NOT NULL, FK to appy_appraisal_periods.id | Appraisal period |
| manager_id | TEXT | NOT NULL, FK to appy_managers.user_id | Conducting manager |
| template_id | UUID | FK to appy_appraisal_templates.id | Template used |
| status | appy_appraisal_status | DEFAULT 'pending' | Appraisal status |
| payment_status | appy_appraisal_status | NULL | Payment processing status |
| submitted_at | TIMESTAMPTZ | NULL | Submission timestamp |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |
| last_edited_at | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP | Last edit timestamp |
| revision_number | INTEGER | DEFAULT 1 | Revision number |
| is_revision | BOOLEAN | DEFAULT false | Revision flag |
| original_submission_date | TIMESTAMPTZ | NULL | Original submission date |
| **Legacy Questions** |
| question_1 | TEXT | NULL | Legacy question 1 response |
| question_2 | TEXT | NULL | Legacy question 2 response |
| question_3 | TEXT | NULL | Legacy question 3 response |
| question_4 | TEXT | NULL | Legacy question 4 response |
| question_5 | TEXT | NULL | Legacy question 5 response |
| **Enhanced Evaluation Fields** |
| key_contributions | TEXT | NULL | Major achievements and contributions |
| extra_initiatives | TEXT | NULL | Additional projects and initiatives |
| performance_lacking | TEXT | NULL | Areas needing improvement |
| discipline_rating | INTEGER | NULL | Discipline rating (1-5 scale) |
| discipline_comment | TEXT | NULL | Discipline rating comments |
| days_off_taken | INTEGER | NULL | Actual days off taken |
| impact_rating | INTEGER | NULL | Impact rating (1-5 scale) |
| impact_comment | TEXT | NULL | Impact rating comments |
| quality_rating | INTEGER | NULL | Quality rating (1-5 scale) |
| quality_comment | TEXT | NULL | Quality rating comments |
| collaboration_rating | INTEGER | NULL | Collaboration rating (1-5 scale) |
| collaboration_comment | TEXT | NULL | Collaboration rating comments |
| skill_growth_rating | INTEGER | NULL | Skill growth rating (1-5 scale) |
| skill_growth_comment | TEXT | NULL | Skill growth rating comments |
| readiness_promotion | TEXT | NULL | Promotion readiness assessment |
| readiness_comment | TEXT | NULL | Promotion readiness comments |
| compensation_recommendation | TEXT | NULL | Compensation recommendations |

#### appy_appraisal_periods
Time periods for conducting appraisals.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Period identifier |
| name | TEXT | NOT NULL | Period name (e.g., "Q1 2024") |
| start_date | DATE | NOT NULL | Period start date |
| end_date | DATE | NOT NULL | Period end date |
| status | TEXT | DEFAULT 'draft' | Period status |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |

#### appy_appraisal_templates
Customizable appraisal templates with JSONB question structure.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Template identifier |
| name | VARCHAR(255) | NOT NULL | Template name |
| description | TEXT | NULL | Template description |
| questions | JSONB | NOT NULL, DEFAULT '[]' | Template questions structure |
| department_id | UUID | FK to appy_departments.id | Department filter |
| role_filter | TEXT | NULL | Role-based filter |
| is_active | BOOLEAN | DEFAULT true | Template active status |
| is_default | BOOLEAN | DEFAULT false | Default template flag |
| version | INTEGER | DEFAULT 1 | Template version |
| created_by | VARCHAR(255) | NULL | Template creator |
| created_at | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP | Update timestamp |

#### appy_template_usage
Template usage tracking and analytics.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Usage record identifier |
| template_id | UUID | FK to appy_appraisal_templates.id | Template reference |
| period_id | UUID | FK to appy_appraisal_periods.id | Period reference |
| manager_id | VARCHAR(255) | NOT NULL | Manager using template |
| usage_count | INTEGER | DEFAULT 0 | Number of times used |
| created_at | TIMESTAMPTZ | DEFAULT CURRENT_TIMESTAMP | Creation timestamp |

### Multi-Level Approval System

#### appy_approval_workflows
Master workflow orchestration for multi-level approvals.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Workflow identifier |
| appraisal_id | UUID | FK to appy_appraisals.id | Associated appraisal |
| workflow_type | VARCHAR(50) | DEFAULT 'standard' | Workflow type |
| current_level | INTEGER | DEFAULT 1 | Current approval level |
| total_levels | INTEGER | DEFAULT 1 | Total approval levels |
| status | VARCHAR(50) | DEFAULT 'pending' | Workflow status |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT now() | Update timestamp |
| completed_at | TIMESTAMPTZ | NULL | Completion timestamp |

#### appy_approval_steps
Individual approval steps within workflows.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Step identifier |
| workflow_id | UUID | FK to appy_approval_workflows.id | Parent workflow |
| step_level | INTEGER | NOT NULL | Step level in sequence |
| approver_id | VARCHAR(255) | NOT NULL | Approver user ID |
| approver_role | VARCHAR(50) | NULL | Approver role |
| step_type | VARCHAR(50) | DEFAULT 'required' | Step type (required/optional) |
| status | VARCHAR(50) | DEFAULT 'pending' | Step status |
| approved_at | TIMESTAMPTZ | NULL | Approval timestamp |
| rejected_at | TIMESTAMPTZ | NULL | Rejection timestamp |
| rejection_reason | TEXT | NULL | Rejection reason |
| comments | TEXT | NULL | Approver comments |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |

#### appy_approval_history
Complete audit trail of all approval actions.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | History record identifier |
| workflow_id | UUID | FK to appy_approval_workflows.id | Workflow reference |
| step_id | UUID | FK to appy_approval_steps.id | Step reference |
| action | VARCHAR(50) | NOT NULL | Action taken |
| actor_id | VARCHAR(255) | NOT NULL | User performing action |
| actor_name | VARCHAR(255) | NULL | Actor display name |
| previous_status | VARCHAR(50) | NULL | Previous status |
| new_status | VARCHAR(50) | NULL | New status |
| comments | TEXT | NULL | Action comments |
| metadata | JSONB | NULL | Additional action metadata |
| created_at | TIMESTAMPTZ | DEFAULT now() | Action timestamp |

#### appy_approval_delegations
Approval delegation management for temporary authority transfer.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Delegation identifier |
| original_approver_id | VARCHAR(255) | NOT NULL | Original approver |
| delegate_approver_id | VARCHAR(255) | NOT NULL | Delegate approver |
| workflow_id | UUID | FK to appy_approval_workflows.id | Specific workflow (optional) |
| delegation_reason | TEXT | NULL | Reason for delegation |
| is_active | BOOLEAN | DEFAULT true | Delegation active status |
| created_by | VARCHAR(255) | NOT NULL | Delegation creator |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |
| expires_at | TIMESTAMPTZ | NULL | Delegation expiration |

## Custom Enums

### appy_appraisal_status
Appraisal lifecycle status values.
- `draft` - Initial draft state
- `submitted` - Submitted for approval
- `completed` - Fully completed
- `pending` - Pending review/approval
- `ready-to-pay` - Ready for payment processing
- `contact-manager` - Requires manager contact

### appy_user_role
System user roles with hierarchical permissions.
- `super-admin` - Full system access
- `hr-admin` - HR management functions
- `manager` - Employee appraisal management
- `accountant` - Financial data access

### appy_compensation_type
Employee compensation rate types.
- `hourly` - Hourly rate compensation
- `monthly` - Monthly salary
- `yearly` - Annual salary

### appy_pto_request_type
Types of PTO requests.
- `vacation` - Vacation time
- `sick` - Sick leave
- `personal` - Personal time off
- `emergency` - Emergency leave

### appy_pto_status
PTO request status values.
- `pending` - Awaiting approval
- `approved` - Approved by manager
- `rejected` - Rejected by manager
- `cancelled` - Cancelled by employee

## HR Management Tables

### appy_employee_feedback
Employee feedback and complaint management system.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Feedback identifier |
| submitter_id | UUID | FK to appy_employees.id | Employee submitting feedback |
| target_employee_id | UUID | FK to appy_employees.id | Target employee (if applicable) |
| feedback_type | VARCHAR(50) | NOT NULL | Type of feedback |
| category | VARCHAR(50) | NULL | Feedback category |
| subject | VARCHAR(255) | NOT NULL | Feedback subject |
| message | TEXT | NOT NULL | Feedback message content |
| is_anonymous | BOOLEAN | DEFAULT false | Anonymous submission flag |
| priority | VARCHAR(20) | DEFAULT 'medium' | Feedback priority level |
| status | VARCHAR(50) | DEFAULT 'pending' | Processing status |
| hr_response | TEXT | NULL | HR response to feedback |
| hr_notes | TEXT | NULL | Internal HR notes |
| reviewed_by | TEXT | FK to appy_managers.user_id | HR reviewer |
| reviewed_at | TIMESTAMPTZ | NULL | Review timestamp |
| resolution_summary | TEXT | NULL | Resolution summary |
| resolved_by | TEXT | FK to appy_managers.user_id | Resolver |
| resolved_at | TIMESTAMPTZ | NULL | Resolution timestamp |
| requires_followup | BOOLEAN | DEFAULT false | Follow-up required flag |
| followup_date | DATE | NULL | Scheduled follow-up date |
| followup_notes | TEXT | NULL | Follow-up notes |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT now() | Update timestamp |

### appy_feedback_comments
Threaded comments for feedback discussions.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Comment identifier |
| feedback_id | UUID | FK to appy_employee_feedback.id | Parent feedback |
| commenter_id | TEXT | FK to appy_managers.user_id | Comment author |
| comment | TEXT | NOT NULL | Comment content |
| is_internal | BOOLEAN | DEFAULT true | Internal HR comment flag |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT now() | Update timestamp |

### appy_feedback_attachments
File attachments for feedback submissions.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Attachment identifier |
| feedback_id | UUID | FK to appy_employee_feedback.id | Parent feedback |
| filename | VARCHAR(255) | NOT NULL | Original filename |
| file_path | VARCHAR(500) | NOT NULL | Storage file path |
| file_size | INTEGER | NULL | File size in bytes |
| mime_type | VARCHAR(100) | NULL | File MIME type |
| uploaded_by | UUID | FK to appy_employees.id | Uploader |
| created_at | TIMESTAMPTZ | DEFAULT now() | Upload timestamp |

### appy_feedback_status_history
Status change history for feedback tracking.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | History record identifier |
| feedback_id | UUID | FK to appy_employee_feedback.id | Parent feedback |
| previous_status | VARCHAR(50) | NULL | Previous status |
| new_status | VARCHAR(50) | NOT NULL | New status |
| changed_by | TEXT | FK to appy_managers.user_id | Status changer |
| change_reason | TEXT | NULL | Reason for status change |
| created_at | TIMESTAMPTZ | DEFAULT now() | Change timestamp |

## Employee Performance & PTO Tables

### appy_employee_kpis
Employee key performance indicators tracking.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | KPI identifier |
| employee_id | UUID | NOT NULL, FK to appy_employees.id | Employee reference |
| kpi_name | VARCHAR(255) | NOT NULL | KPI name/metric |
| kpi_value | VARCHAR(255) | NULL | Current KPI value |
| kpi_target | VARCHAR(255) | NULL | Target KPI value |
| kpi_unit | VARCHAR(100) | NULL | KPI unit of measurement |
| period | VARCHAR(100) | NULL | KPI measurement period |
| description | TEXT | NULL | KPI description |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT now() | Update timestamp |

### appy_employee_pto_balances
Employee PTO balance tracking by year.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Balance identifier |
| employee_id | UUID | NOT NULL, FK to appy_employees.id | Employee reference |
| year | INTEGER | NOT NULL, DEFAULT EXTRACT(year FROM CURRENT_DATE) | Balance year |
| total_days | INTEGER | NOT NULL, DEFAULT 7 | Total PTO days allocated |
| used_days | INTEGER | NOT NULL, DEFAULT 0 | PTO days used |
| available_days | INTEGER | NULL | Available PTO days (calculated) |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT now() | Update timestamp |

### appy_pto_requests
PTO request management and approval workflow.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Request identifier |
| employee_id | UUID | NOT NULL, FK to appy_employees.id | Requesting employee |
| manager_id | TEXT | NOT NULL, FK to appy_managers.user_id | Approving manager |
| request_type | appy_pto_request_type | NOT NULL, DEFAULT 'vacation' | Type of PTO request |
| start_date | DATE | NOT NULL | PTO start date |
| end_date | DATE | NOT NULL | PTO end date |
| days_requested | INTEGER | NOT NULL | Number of days requested |
| reason | TEXT | NULL | Reason for PTO request |
| status | appy_pto_status | NOT NULL, DEFAULT 'pending' | Request status |
| approved_by | TEXT | FK to appy_managers.user_id | Approving manager |
| approved_at | TIMESTAMPTZ | NULL | Approval timestamp |
| rejected_reason | TEXT | NULL | Rejection reason |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT now() | Update timestamp |

## Communication & Notification Tables

### appy_email_templates
Email template management for system notifications.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Template identifier |
| template_key | VARCHAR(100) | NOT NULL, UNIQUE | Template key identifier |
| template_name | VARCHAR(200) | NOT NULL | Human-readable template name |
| subject_template | TEXT | NOT NULL | Email subject template |
| body_template | TEXT | NOT NULL | Email body template |
| template_type | VARCHAR(50) | DEFAULT 'notification' | Template type |
| is_active | BOOLEAN | DEFAULT true | Template active status |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT now() | Update timestamp |
| updated_by | VARCHAR(100) | NULL | Last updater |

### appy_email_settings
Email system configuration and settings.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Setting identifier |
| setting_key | VARCHAR(100) | NOT NULL, UNIQUE | Setting key |
| setting_value | TEXT | NOT NULL | Setting value |
| setting_type | VARCHAR(20) | DEFAULT 'string' | Value type |
| description | TEXT | NULL | Setting description |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT now() | Update timestamp |
| updated_by | VARCHAR(100) | NULL | Last updater |

### appy_notification_log
Email delivery tracking and logging.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Log entry identifier |
| notification_type | VARCHAR(100) | NOT NULL | Type of notification |
| recipient_email | VARCHAR(255) | NOT NULL | Recipient email address |
| subject | TEXT | NULL | Email subject |
| body | TEXT | NULL | Email body content |
| email_sent | BOOLEAN | DEFAULT false | Email sent status |
| email_sent_at | TIMESTAMPTZ | NULL | Email sent timestamp |
| email_error | TEXT | NULL | Email error message |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |
| created_by | VARCHAR(100) | NULL | Creator |

### appy_scheduled_notifications
Scheduled notification management system.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Notification identifier |
| notification_type | VARCHAR(100) | NOT NULL | Type of notification |
| recipient_id | VARCHAR(100) | NOT NULL | Recipient user ID |
| recipient_email | VARCHAR(255) | NOT NULL | Recipient email |
| template_key | VARCHAR(100) | NOT NULL | Email template key |
| template_data | JSONB | NULL | Template variable data |
| scheduled_for | TIMESTAMPTZ | NOT NULL | Scheduled delivery time |
| processed | BOOLEAN | DEFAULT false | Processing status |
| processed_at | TIMESTAMPTZ | NULL | Processing timestamp |
| created_at | TIMESTAMPTZ | DEFAULT now() | Creation timestamp |
| created_by | VARCHAR(100) | NULL | Creator |

## System Administration Tables

### appy_audit_log
System-wide audit logging for compliance and tracking.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Log entry identifier |
| action | VARCHAR(50) | NOT NULL | Action performed (INSERT/UPDATE/DELETE) |
| table_name | VARCHAR(100) | NOT NULL | Affected table name |
| record_id | VARCHAR(100) | NULL | Affected record identifier |
| old_values | JSONB | NULL | Previous field values |
| new_values | JSONB | NULL | New field values |
| user_id | VARCHAR(100) | NOT NULL | User performing action |
| created_at | TIMESTAMPTZ | DEFAULT now() | Action timestamp |

## Indexes and Performance

### Primary Indexes
All tables have primary key indexes on their `id` columns (UUID type).

### Foreign Key Indexes
- `appy_employees.department_id` → `appy_departments.id`
- `appy_employees.manager_id` → `appy_managers.user_id`
- `appy_appraisals.employee_id` → `appy_employees.id`
- `appy_appraisals.manager_id` → `appy_managers.user_id`
- `appy_appraisals.period_id` → `appy_appraisal_periods.id`
- All other foreign key relationships as defined in the schema

### Performance Indexes
- `appy_employees.email` (unique index for fast lookups)
- `appy_managers.email` (unique index for authentication)
- `appy_appraisals.status` (for status-based queries)
- `appy_employee_feedback.status` (for HR dashboard queries)
- `appy_audit_log.created_at` (for audit log queries)
- `appy_notification_log.created_at` (for notification history)

### Composite Indexes
- `appy_appraisals(period_id, employee_id)` (unique constraint)
- `appy_employee_pto_balances(employee_id, year)` (unique constraint)
- `appy_approval_steps(workflow_id, step_level)` (workflow ordering)

## Data Integrity Constraints

### Unique Constraints
- `appy_employees.email` - Unique employee emails
- `appy_managers.email` - Unique manager emails
- `appy_email_templates.template_key` - Unique template keys
- `appy_email_settings.setting_key` - Unique setting keys
- `appy_appraisals(period_id, employee_id)` - One appraisal per employee per period

### Check Constraints
- `appy_employee_pto_balances.used_days >= 0` - Non-negative used days
- `appy_employee_pto_balances.total_days >= 0` - Non-negative total days
- `appy_pto_requests.days_requested > 0` - Positive days requested
- `appy_pto_requests.end_date >= start_date` - Valid date ranges

### Cascade Rules
- Employee deletion cascades to related appraisals, KPIs, and PTO records
- Manager deletion updates related employee assignments to NULL
- Department deletion updates related employee assignments to NULL
- Feedback deletion cascades to comments and attachments

This comprehensive database schema supports all aspects of the appraisal system including employee management, performance evaluations, HR processes, multi-level approvals, and system administration.
